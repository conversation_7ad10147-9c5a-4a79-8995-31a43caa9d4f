
        <sql splitStatements="false">
            <![CDATA[
            CREATE OR REPLACE FUNCTION sandf.fn_get_plan_design_report_group_class(
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL,
    includes_param TEXT[] DEFAULT NULL,
    excludes_param TEXT[] DEFAULT NULL,
    includes_quotes_uuid Text[] DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$

DECLARE
    plan_details_key TEXT := 'planDetails';
    quote_record RECORD;
    plan_details JSONB;
    fields JSONB;
    field_item JSONB;
    carriers_array JSONB := '[]'::jsonb;
    sections_array JSONB := '[]'::jsonb;
    section_obj JSONB;
    benefits_array JSONB;
    benefit_obj JSONB;
    carrier_name TEXT;
    group_name TEXT;
    group_display_name TEXT;
    benefit_name TEXT;
    benefit_key TEXT;
    benefit_values JSONB := '{}'::jsonb;
    field_details JSONB;
    field_detail JSONB;
    carrier_value TEXT;
    section_map JSONB := '{}'::jsonb;
    section_original_names JSONB := '{}'::jsonb;
    section_names TEXT[];
    section_name TEXT;
    section_id TEXT;
    section_display_name TEXT;
    benefit_map JSONB := '{}'::jsonb;
    benefit_keys TEXT[];
    benefit_key_item TEXT;
    section_order_record RECORD;
    benefit_order_record RECORD;
    skip_fields TEXT[] := ARRAY['maximumLife', 'maximumADAD'];
    coverage_life_values JSONB := '{}'::jsonb;
    field_values_to_check JSONB := '{}'::jsonb;
    should_skip_field BOOLEAN;
    carrier_check TEXT;
    coverage_value TEXT;
    field_value TEXT;
    carrier_order_map JSONB := '{}'::jsonb;
    ordered_carriers_array JSONB := '[]'::jsonb;
    carrier_item TEXT;
    carrier_order INTEGER;
    quote_uuid_val UUID;
    MAX_BENEFITS_PER_PAGE INTEGER := 16;
    all_benefits JSONB := '[]'::jsonb;
    all_sections JSONB := '[]'::jsonb;
    current_page_benefits INTEGER := 0;
    current_page_sections_count INTEGER := 0;
    current_page_sections JSONB := '[]'::jsonb;
    current_section_benefits JSONB := '[]'::jsonb;
    result_pages JSONB := '[]'::jsonb;
    total_benefits INTEGER;
    total_sections INTEGER;
    current_section_name TEXT;
    current_section_id TEXT;
    current_section_display_name TEXT;
    section_idx INTEGER;
    benefit_idx INTEGER;
    page_object JSONB;
    employee_classes TEXT[];
    employee_class_count INTEGER;
    current_employee_class TEXT;
    class_suffix TEXT;
    class_index INTEGER;
    class_suffix_extracted TEXT;

    -- Class grouping and legend variables (from rate-sheet-v2.sql)
    class_to_letter_map JSONB := '{}'::jsonb;
    class_legend_array TEXT[] := ARRAY[]::TEXT[];
    letter_index INTEGER := 1;
    current_letter TEXT;

    -- Class sections mapping for groupby logic
    class_sections_map JSONB := '{}'::jsonb;
    combined_sections_map JSONB := '{}'::jsonb;
    section_signature TEXT;
    matching_classes TEXT[];
    combined_class_name TEXT;

    -- Variable to track the first carrier name for display purposes
    first_carrier_name TEXT;
    display_carrier_name TEXT;

    -- Configuration variables for filtering and ordering
    config_plan_details JSONB;
    config_overview JSONB;
    config_section_key TEXT;
    config_section_info JSONB;
    config_display_items JSONB;
    mapped_section_key TEXT;
    carrier_has_data BOOLEAN;
    config_section_keys TEXT[];
    config_section_order INTEGER;
    config_json JSONB;

    -- Fixed pagination configuration (dynamic based on classes)
    pagination_config JSONB;
    page_number INTEGER;
    total_pages_per_class INTEGER;
BEGIN
    -- Load configuration from config.json_storage table
    SELECT json_data INTO config_json
    FROM config.json_storage
    WHERE properties = '{"config": "SURVEY_REPORT_CONFIG"}'
    LIMIT 1;

    -- Extract planDetails from the configuration
    config_plan_details := config_json -> 'planDetails';

    -- Extract pagination configuration from the configuration
    pagination_config := config_json -> 'pagination';
    IF pagination_config IS NULL THEN
        -- Default pagination configuration if not found in config
        pagination_config := '[
            [1, 2, 3],
            [4, 5, 6, 7],
            [8, 9]
        ]'::jsonb;
    END IF;

    -- Calculate total pages per class (3 pages per class by default)
    total_pages_per_class := jsonb_array_length(pagination_config);

    -- Get the order of sections from config_plan_details keys (maintains order from config.json)
    SELECT array_agg(key ORDER BY ordinality) INTO config_section_keys
    FROM jsonb_each(config_plan_details) WITH ORDINALITY;

    SELECT COUNT(DISTINCT ec.name), array_agg(DISTINCT ec.name ORDER BY ec.name)
    INTO employee_class_count, employee_classes
    FROM sandf.plan p
    JOIN sandf.quote q ON q.plan_id = p.plan_id
    JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
    JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
    WHERE p.plan_uuid = plan_uuid_param::uuid
    AND ecq.formatted_quote_details IS NOT NULL
    AND ecq.formatted_quote_details != '{}'::jsonb
    AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid));

    -- Build class-to-letter mapping (A, B, C, etc.) for groupby logic
    FOREACH current_employee_class IN ARRAY employee_classes
    LOOP
        current_letter := chr(64 + letter_index); -- A=65, B=66, etc.
        class_to_letter_map := class_to_letter_map || jsonb_build_object(current_employee_class, current_letter);
        class_legend_array := array_append(class_legend_array, current_letter || ' = ' || current_employee_class);
        letter_index := letter_index + 1;
    END LOOP;
    FOR quote_record IN
        SELECT ecq.formatted_quote_details,
               c.description as carrier_description,
               q.quote_id,
               q.quote_uuid,
               ec.name as employee_class_name
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = plan_uuid_param::uuid
        AND ec.name = ANY(employee_classes)
        AND ecq.formatted_quote_details IS NOT NULL
        AND ecq.formatted_quote_details != '{}'::jsonb
        AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid))
    LOOP
        carrier_name := quote_record.carrier_description;

        carrier_order := sandf.get_user_preference_order(
            user_id_param,
            plan_uuid_param,
            quote_record.quote_id,
            quote_record.quote_uuid,
            999999
        );

        IF NOT carrier_order_map ? carrier_name THEN
            carrier_order_map := carrier_order_map || jsonb_build_object(
                carrier_name,
                jsonb_build_object(
                    'order', carrier_order,
                    'quote_id', quote_record.quote_id,
                    'quote_uuid', quote_record.quote_uuid
                )
            );
        END IF;
    END LOOP;

    -- Set the first carrier name for display purposes (before processing benefits)
    FOR carrier_item IN
        SELECT key as carrier_name
        FROM jsonb_each(carrier_order_map)
        ORDER BY (value ->> 'order')::integer ASC, key ASC
        LIMIT 1
    LOOP
        first_carrier_name := carrier_item;
    END LOOP;

    -- Step 1: Collect section data by class for groupby logic
    class_index := 1;
    FOREACH current_employee_class IN ARRAY employee_classes
    LOOP
        class_suffix := current_employee_class;
        current_letter := class_to_letter_map ->> current_employee_class;

        -- Initialize class sections map for this class
        IF NOT class_sections_map ? current_employee_class THEN
            class_sections_map := class_sections_map || jsonb_build_object(current_employee_class, '{}'::jsonb);
        END IF;

        FOR quote_record IN
            SELECT ecq.formatted_quote_details,
                   c.description as carrier_description,
                   q.quote_id,
                   q.quote_uuid
            FROM sandf.plan p
            JOIN sandf.quote q ON q.plan_id = p.plan_id
            JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
            JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
            JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
            WHERE p.plan_uuid = plan_uuid_param::uuid
            AND ec.name = current_employee_class
            AND ecq.formatted_quote_details IS NOT NULL
            AND ecq.formatted_quote_details != '{}'::jsonb
            AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid))
            ORDER BY
                COALESCE((carrier_order_map -> c.description ->> 'order')::integer, 999999) ASC,
                c.description ASC
        LOOP
            carrier_name := quote_record.carrier_description;
            plan_details := quote_record.formatted_quote_details -> plan_details_key;

            IF plan_details IS NOT NULL AND jsonb_typeof(plan_details) = 'object' THEN
                fields := plan_details -> 'fields';
                IF fields IS NOT NULL AND jsonb_typeof(fields) = 'array' THEN
                    FOR field_item IN SELECT jsonb_array_elements(fields)
                    LOOP
                        group_name := field_item ->> 'groupName';
                        IF group_name IS NULL THEN
                            group_name := field_item ->> 'name';
                        END IF;

                        -- Check if this section is in our configuration
                        mapped_section_key := NULL;
                        config_section_info := NULL;

                        -- Map lifeInsuranceADAD to both lifeInsurance and ADAD sections
                        IF group_name = 'lifeInsuranceADAD' THEN
                            -- Process as lifeInsurance first
                            IF config_plan_details ? 'lifeInsurance' THEN
                                mapped_section_key := 'lifeInsurance';
                                config_section_info := config_plan_details -> 'lifeInsurance';
                            END IF;
                        ELSIF config_plan_details ? group_name THEN
                            mapped_section_key := group_name;
                            config_section_info := config_plan_details -> group_name;
                        END IF;

                        -- Only process if section is in configuration
                        IF config_section_info IS NOT NULL AND
                           (includes_param IS NULL OR group_name = ANY(includes_param)) AND
                           (excludes_param IS NULL OR NOT (group_name = ANY(excludes_param))) THEN

                            group_display_name := config_section_info ->> mapped_section_key;
                            config_display_items := config_section_info -> 'displayItems';

                            -- Initialize section in class_sections_map if not exists
                            IF NOT (class_sections_map -> current_employee_class) ? mapped_section_key THEN
                                class_sections_map := jsonb_set(
                                    class_sections_map,
                                    ARRAY[current_employee_class, mapped_section_key],
                                    jsonb_build_object(
                                        'name', group_display_name,
                                        'id', lower(replace(replace(replace(mapped_section_key, ' ', ''), '&', ''), '''', '')),
                                        'values', '{}'::jsonb
                                    )
                                );
                            END IF;

                            field_details := field_item -> 'fields';
                            IF field_details IS NOT NULL AND jsonb_typeof(field_details) = 'array' THEN
                                FOR field_detail IN SELECT jsonb_array_elements(field_details)
                                LOOP
                                    benefit_key := field_detail ->> 'name';
                                    carrier_value := field_detail ->> 'value';

                                    -- Only process fields that are in displayItems configuration
                                    IF config_display_items ? benefit_key THEN
                                        benefit_name := config_display_items ->> benefit_key;

                                        IF carrier_value IS NULL OR trim(carrier_value) = '' THEN
                                            carrier_value := '-';
                                        ELSE
                                            BEGIN
                                                PERFORM sandf.safe_parse_numeric(carrier_value);
                                            EXCEPTION WHEN OTHERS THEN
                                                carrier_value := '-';
                                            END;
                                        END IF;

                                        -- Use "Current" for first carrier, otherwise use actual name
                                        IF carrier_name = first_carrier_name THEN
                                            display_carrier_name := 'Current';
                                        ELSE
                                            display_carrier_name := carrier_name;
                                        END IF;

                                        IF benefit_key = 'coverageLife' THEN
                                            coverage_life_values := coverage_life_values || jsonb_build_object(
                                                display_carrier_name || '_' || class_suffix,
                                                carrier_value
                                            );
                                        END IF;

                                        IF benefit_key = ANY(skip_fields) THEN
                                            IF NOT field_values_to_check ? (benefit_key || '_' || class_suffix) THEN
                                                field_values_to_check := field_values_to_check || jsonb_build_object(
                                                    benefit_key || '_' || class_suffix,
                                                    '{}'::jsonb
                                                );
                                            END IF;
                                            field_values_to_check := jsonb_set(
                                                field_values_to_check,
                                                ARRAY[benefit_key || '_' || class_suffix, display_carrier_name],
                                                to_jsonb(carrier_value)
                                            );
                                        END IF;

                                        -- Store benefit in class_sections_map structure
                                        -- Initialize benefit if not exists
                                        IF NOT (class_sections_map -> current_employee_class -> mapped_section_key -> 'values') ? benefit_key THEN
                                            class_sections_map := jsonb_set(
                                                class_sections_map,
                                                ARRAY[current_employee_class, mapped_section_key, 'values', benefit_key],
                                                jsonb_build_object(
                                                    'name', benefit_name,
                                                    'key', benefit_key,
                                                    'values', '{}'::jsonb
                                                )
                                            );
                                        END IF;

                                        -- Set carrier value for this benefit
                                        class_sections_map := jsonb_set(
                                            class_sections_map,
                                            ARRAY[current_employee_class, mapped_section_key, 'values', benefit_key, 'values', display_carrier_name],
                                            to_jsonb(carrier_value)
                                        );
                                    END IF;
                                END LOOP;
                            END IF;

                            -- Handle ADAD section separately for lifeInsuranceADAD
                            IF group_name = 'lifeInsuranceADAD' AND config_plan_details ? 'ADAD' THEN
                                config_section_info := config_plan_details -> 'ADAD';
                                group_display_name := config_section_info ->> 'ADAD';
                                config_display_items := config_section_info -> 'displayItems';

                                -- Initialize ADAD section in class_sections_map if not exists
                                IF NOT (class_sections_map -> current_employee_class) ? 'ADAD' THEN
                                    class_sections_map := jsonb_set(
                                        class_sections_map,
                                        ARRAY[current_employee_class, 'ADAD'],
                                        jsonb_build_object(
                                            'name', group_display_name,
                                            'id', 'adad',
                                            'values', '{}'::jsonb
                                        )
                                    );
                                END IF;

                                -- Process ADAD fields
                                field_details := field_item -> 'fields';
                                IF field_details IS NOT NULL AND jsonb_typeof(field_details) = 'array' THEN
                                    FOR field_detail IN SELECT jsonb_array_elements(field_details)
                                    LOOP
                                        benefit_key := field_detail ->> 'name';
                                        carrier_value := field_detail ->> 'value';

                                        -- Only process ADAD fields that are in displayItems configuration
                                        IF config_display_items ? benefit_key THEN
                                            benefit_name := config_display_items ->> benefit_key;

                                            IF carrier_value IS NULL OR trim(carrier_value) = '' THEN
                                                carrier_value := '-';
                                            ELSE
                                                BEGIN
                                                    PERFORM sandf.safe_parse_numeric(carrier_value);
                                                EXCEPTION WHEN OTHERS THEN
                                                    carrier_value := '-';
                                                END;
                                            END IF;

                                            -- Use "Current" for first carrier, otherwise use actual name
                                            IF carrier_name = first_carrier_name THEN
                                                display_carrier_name := 'Current';
                                            ELSE
                                                display_carrier_name := carrier_name;
                                            END IF;

                                            IF benefit_key = ANY(skip_fields) THEN
                                                IF NOT field_values_to_check ? (benefit_key || '_' || class_suffix) THEN
                                                    field_values_to_check := field_values_to_check || jsonb_build_object(
                                                        benefit_key || '_' || class_suffix,
                                                        '{}'::jsonb
                                                    );
                                                END IF;
                                                field_values_to_check := jsonb_set(
                                                    field_values_to_check,
                                                    ARRAY[benefit_key || '_' || class_suffix, display_carrier_name],
                                                    to_jsonb(carrier_value)
                                                );
                                            END IF;

                                            -- Store ADAD benefit in class_sections_map structure
                                            -- Initialize benefit if not exists
                                            IF NOT (class_sections_map -> current_employee_class -> 'ADAD' -> 'values') ? benefit_key THEN
                                                class_sections_map := jsonb_set(
                                                    class_sections_map,
                                                    ARRAY[current_employee_class, 'ADAD', 'values', benefit_key],
                                                    jsonb_build_object(
                                                        'name', benefit_name,
                                                        'key', benefit_key,
                                                        'values', '{}'::jsonb
                                                    )
                                                );
                                            END IF;

                                            -- Set carrier value for this ADAD benefit
                                            class_sections_map := jsonb_set(
                                                class_sections_map,
                                                ARRAY[current_employee_class, 'ADAD', 'values', benefit_key, 'values', display_carrier_name],
                                                to_jsonb(carrier_value)
                                            );
                                        END IF;
                                    END LOOP;
                                END IF;
                            END IF;
                        END IF;
                    END LOOP;
                ELSE
                    DECLARE
                        benefit_data JSONB;
                        field_key TEXT;
                        show_value TEXT;
                    BEGIN
                        FOR benefit_key IN SELECT jsonb_object_keys(plan_details)
                        LOOP
                            -- Check if this section is in our configuration
                            mapped_section_key := NULL;
                            config_section_info := NULL;

                            -- Map lifeInsuranceADAD to both lifeInsurance and ADAD sections
                            IF benefit_key = 'lifeInsuranceADAD' THEN
                                -- Process as lifeInsurance first
                                IF config_plan_details ? 'lifeInsurance' THEN
                                    mapped_section_key := 'lifeInsurance';
                                    config_section_info := config_plan_details -> 'lifeInsurance';
                                END IF;
                            ELSIF config_plan_details ? benefit_key THEN
                                mapped_section_key := benefit_key;
                                config_section_info := config_plan_details -> benefit_key;
                            END IF;

                            -- Only process if section is in configuration
                            IF config_section_info IS NOT NULL AND
                               (includes_param IS NULL OR benefit_key = ANY(includes_param)) AND
                               (excludes_param IS NULL OR NOT (benefit_key = ANY(excludes_param))) THEN

                                benefit_data := plan_details -> benefit_key;

                                IF benefit_data IS NULL OR jsonb_typeof(benefit_data) != 'object' THEN
                                    CONTINUE;
                                END IF;

                                show_value := benefit_data ->> 'show';
                                IF show_value IS NOT NULL AND show_value::boolean = false THEN
                                    CONTINUE;
                                END IF;

                                section_display_name := config_section_info ->> mapped_section_key;
                                config_display_items := config_section_info -> 'displayItems';

                                -- Initialize section in class_sections_map if not exists
                                IF NOT (class_sections_map -> current_employee_class) ? mapped_section_key THEN
                                    class_sections_map := jsonb_set(
                                        class_sections_map,
                                        ARRAY[current_employee_class, mapped_section_key],
                                        jsonb_build_object(
                                            'name', section_display_name,
                                            'id', lower(replace(replace(replace(mapped_section_key, ' ', ''), '&', ''), '''', '')),
                                            'values', '{}'::jsonb
                                        )
                                    );
                                END IF;

                                FOR field_key IN SELECT jsonb_object_keys(benefit_data)
                                LOOP
                                    IF field_key = 'show' THEN
                                        CONTINUE;
                                    END IF;

                                    -- Only process fields that are in displayItems configuration
                                    IF config_display_items ? field_key THEN
                                        benefit_name := config_display_items ->> field_key;

                                        carrier_value := benefit_data ->> field_key;
                                        IF carrier_value IS NULL OR trim(carrier_value) = '' THEN
                                            carrier_value := '-';
                                        ELSE
                                            BEGIN
                                                PERFORM sandf.safe_parse_numeric(carrier_value);
                                            EXCEPTION WHEN OTHERS THEN
                                                carrier_value := '-';
                                            END;
                                        END IF;

                                        -- Use "Current" for first carrier, otherwise use actual name
                                        IF carrier_name = first_carrier_name THEN
                                            display_carrier_name := 'Current';
                                        ELSE
                                            display_carrier_name := carrier_name;
                                        END IF;

                                        IF field_key = 'coverageLife' THEN
                                            coverage_life_values := coverage_life_values || jsonb_build_object(
                                                display_carrier_name || '_' || class_suffix,
                                                carrier_value
                                            );
                                        END IF;

                                        IF field_key = ANY(skip_fields) THEN
                                            IF NOT field_values_to_check ? (field_key || '_' || class_suffix) THEN
                                                field_values_to_check := field_values_to_check || jsonb_build_object(
                                                    field_key || '_' || class_suffix,
                                                    '{}'::jsonb
                                                );
                                            END IF;
                                            field_values_to_check := jsonb_set(
                                                field_values_to_check,
                                                ARRAY[field_key || '_' || class_suffix, display_carrier_name],
                                                to_jsonb(carrier_value)
                                            );
                                        END IF;

                                        -- Store benefit in class_sections_map structure
                                        -- Initialize benefit if not exists
                                        IF NOT (class_sections_map -> current_employee_class -> mapped_section_key -> 'values') ? field_key THEN
                                            class_sections_map := jsonb_set(
                                                class_sections_map,
                                                ARRAY[current_employee_class, mapped_section_key, 'values', field_key],
                                                jsonb_build_object(
                                                    'name', benefit_name,
                                                    'key', field_key,
                                                    'values', '{}'::jsonb
                                                )
                                            );
                                        END IF;

                                        -- Set carrier value for this benefit
                                        class_sections_map := jsonb_set(
                                            class_sections_map,
                                            ARRAY[current_employee_class, mapped_section_key, 'values', field_key, 'values', display_carrier_name],
                                            to_jsonb(carrier_value)
                                        );
                                    END IF;
                                END LOOP;

                                -- Handle ADAD section separately for lifeInsuranceADAD
                                IF benefit_key = 'lifeInsuranceADAD' AND config_plan_details ? 'ADAD' THEN
                                    config_section_info := config_plan_details -> 'ADAD';
                                    section_display_name := config_section_info ->> 'ADAD';
                                    config_display_items := config_section_info -> 'displayItems';

                                    -- Initialize ADAD section in class_sections_map if not exists
                                    IF NOT (class_sections_map -> current_employee_class) ? 'ADAD' THEN
                                        class_sections_map := jsonb_set(
                                            class_sections_map,
                                            ARRAY[current_employee_class, 'ADAD'],
                                            jsonb_build_object(
                                                'name', section_display_name,
                                                'id', 'adad',
                                                'values', '{}'::jsonb
                                            )
                                        );
                                    END IF;

                                    -- Process ADAD fields
                                    FOR field_key IN SELECT jsonb_object_keys(benefit_data)
                                    LOOP
                                        IF field_key = 'show' THEN
                                            CONTINUE;
                                        END IF;

                                        -- Only process ADAD fields that are in displayItems configuration
                                        IF config_display_items ? field_key THEN
                                            benefit_name := config_display_items ->> field_key;

                                            carrier_value := benefit_data ->> field_key;
                                            IF carrier_value IS NULL OR trim(carrier_value) = '' THEN
                                                carrier_value := '-';
                                            ELSE
                                                BEGIN
                                                    PERFORM sandf.safe_parse_numeric(carrier_value);
                                                EXCEPTION WHEN OTHERS THEN
                                                    carrier_value := '-';
                                                END;
                                            END IF;

                                            -- Use "Current" for first carrier, otherwise use actual name
                                            IF carrier_name = first_carrier_name THEN
                                                display_carrier_name := 'Current';
                                            ELSE
                                                display_carrier_name := carrier_name;
                                            END IF;

                                            IF field_key = ANY(skip_fields) THEN
                                                IF NOT field_values_to_check ? (field_key || '_' || class_suffix) THEN
                                                    field_values_to_check := field_values_to_check || jsonb_build_object(
                                                        field_key || '_' || class_suffix,
                                                        '{}'::jsonb
                                                    );
                                                END IF;
                                                field_values_to_check := jsonb_set(
                                                    field_values_to_check,
                                                    ARRAY[field_key || '_' || class_suffix, display_carrier_name],
                                                    to_jsonb(carrier_value)
                                                );
                                            END IF;

                                            -- Store ADAD benefit in class_sections_map structure
                                            -- Initialize benefit if not exists
                                            IF NOT (class_sections_map -> current_employee_class -> 'ADAD' -> 'values') ? field_key THEN
                                                class_sections_map := jsonb_set(
                                                    class_sections_map,
                                                    ARRAY[current_employee_class, 'ADAD', 'values', field_key],
                                                    jsonb_build_object(
                                                        'name', benefit_name,
                                                        'key', field_key,
                                                        'values', '{}'::jsonb
                                                    )
                                                );
                                            END IF;

                                            -- Set carrier value for this ADAD benefit
                                            class_sections_map := jsonb_set(
                                                class_sections_map,
                                                ARRAY[current_employee_class, 'ADAD', 'values', field_key, 'values', display_carrier_name],
                                                to_jsonb(carrier_value)
                                            );
                                        END IF;
                                    END LOOP;
                                END IF;
                            END IF;
                        END LOOP;
                    END;
                END IF;
            END IF;
        END LOOP;

        class_index := class_index + 1;
    END LOOP;

    -- Step 2: Normalize class_sections_map to ensure all benefits have values for all carriers
    DECLARE
        all_carriers TEXT[];
        class_name TEXT;
        section_key TEXT;
        benefit_key_norm TEXT;
        carrier_name_norm TEXT;
        section_data JSONB;
        benefit_data JSONB;
    BEGIN
        -- Get all unique carrier names
        SELECT array_agg(DISTINCT key ORDER BY key) INTO all_carriers
        FROM jsonb_each(carrier_order_map);

        -- For each class and section, ensure all benefits have all carriers
        FOR class_name IN SELECT jsonb_object_keys(class_sections_map)
        LOOP
            FOR section_key IN SELECT jsonb_object_keys(class_sections_map -> class_name)
            LOOP
                section_data := class_sections_map -> class_name -> section_key;
                FOR benefit_key_norm IN SELECT jsonb_object_keys(section_data -> 'values')
                LOOP
                    benefit_data := section_data -> 'values' -> benefit_key_norm;

                    -- Add missing carriers with "-" value
                    FOREACH carrier_name_norm IN ARRAY all_carriers
                    LOOP
                        IF NOT (benefit_data -> 'values') ? carrier_name_norm THEN
                            class_sections_map := jsonb_set(
                                class_sections_map,
                                ARRAY[class_name, section_key, 'values', benefit_key_norm, 'values', carrier_name_norm],
                                to_jsonb('-'::text)
                            );
                        END IF;
                    END LOOP;
                END LOOP;
            END LOOP;
        END LOOP;
    END;

    -- Step 3: Build ordered carriers array
    FOR carrier_item IN
        SELECT key as carrier_name
        FROM jsonb_each(carrier_order_map)
        ORDER BY (value ->> 'order')::integer ASC, key ASC
    LOOP
        -- Use "Current" for the first carrier, otherwise use actual name
        IF carrier_item = first_carrier_name THEN
            display_carrier_name := 'Current';
        ELSE
            display_carrier_name := carrier_item;
        END IF;

        ordered_carriers_array := ordered_carriers_array || jsonb_build_array(display_carrier_name);
    END LOOP;

    -- Step 4: Group sections by matching benefits and create final sections array (from rate-sheet-v2.sql logic)
    DECLARE
        all_section_keys TEXT[];
        section_key_item TEXT;
        class_section JSONB;
        signature_to_classes JSONB := '{}'::jsonb;
        signature_to_section JSONB := '{}'::jsonb;
        processed_signatures TEXT[] := ARRAY[]::TEXT[];
    BEGIN
        -- Get all unique section keys across all classes
        SELECT array_agg(DISTINCT subq.section_key)
        INTO all_section_keys
        FROM (
            SELECT jsonb_object_keys(class_data.class_sections) as section_key
            FROM jsonb_each(class_sections_map) as class_data(class_name, class_sections)
        ) subq;

        -- For each section key, group classes with matching benefit values
        FOREACH section_key_item IN ARRAY all_section_keys
        LOOP
            signature_to_classes := '{}'::jsonb;
            signature_to_section := '{}'::jsonb;

            -- Check each class for this section key
            FOR current_employee_class IN
                SELECT jsonb_object_keys(class_sections_map)
            LOOP
                class_section := class_sections_map -> current_employee_class -> section_key_item;

                IF class_section IS NOT NULL THEN
                    -- Create signature from benefit values for matching
                    section_signature := '';
                    FOR benefit_key IN
                        SELECT jsonb_object_keys(class_section -> 'values')
                    LOOP
                        FOR carrier_name IN
                            SELECT jsonb_object_keys((class_section -> 'values' -> benefit_key -> 'values'))
                        LOOP
                            section_signature := section_signature || benefit_key || ':' || carrier_name || ':' ||
                                COALESCE(class_section -> 'values' -> benefit_key -> 'values' -> carrier_name ->> 0, '') || ';';
                        END LOOP;
                    END LOOP;

                    -- Group classes by signature
                    IF NOT signature_to_classes ? section_signature THEN
                        signature_to_classes := signature_to_classes || jsonb_build_object(
                            section_signature,
                            jsonb_build_array(current_employee_class)
                        );
                        signature_to_section := signature_to_section || jsonb_build_object(
                            section_signature,
                            class_section
                        );
                    ELSE
                        signature_to_classes := jsonb_set(
                            signature_to_classes,
                            ARRAY[section_signature],
                            (signature_to_classes -> section_signature) || jsonb_build_array(current_employee_class)
                        );
                    END IF;
                END IF;
            END LOOP;
            -- Create combined sections for each signature
            FOR section_signature IN
                SELECT jsonb_object_keys(signature_to_classes)
            LOOP
                matching_classes := ARRAY(SELECT jsonb_array_elements_text(signature_to_classes -> section_signature));
                section_obj := signature_to_section -> section_signature;

                -- Build className based on matching classes
                IF array_length(matching_classes, 1) = employee_class_count THEN
                    combined_class_name := 'ALL';
                ELSE
                    -- Convert class names to letters and join
                    DECLARE
                        class_letters TEXT[] := ARRAY[]::TEXT[];
                        class_item TEXT;
                    BEGIN
                        FOREACH class_item IN ARRAY matching_classes
                        LOOP
                            class_letters := array_append(class_letters, class_to_letter_map ->> class_item);
                        END LOOP;
                        combined_class_name := array_to_string(class_letters, ',');
                    END;
                END IF;

                -- Create final section with combined class name
                section_id := (section_obj ->> 'id') || combined_class_name;
                section_display_name := (section_obj ->> 'name') || ' ' || combined_class_name;

                -- Convert benefits from class structure to final structure
                benefits_array := '[]'::jsonb;
                FOR benefit_key IN
                    SELECT jsonb_object_keys(section_obj -> 'values')
                LOOP
                    benefit_obj := section_obj -> 'values' -> benefit_key;
                    benefit_obj := benefit_obj || jsonb_build_object('section', section_id);

                    -- Check if benefit has any non-empty values across all carriers (following ungroup-multi logic)
                    DECLARE
                        carrier_has_data BOOLEAN := FALSE;
                        carrier_item TEXT;
                        carrier_value TEXT;
                    BEGIN
                        FOR carrier_item IN SELECT jsonb_object_keys(benefit_obj -> 'values')
                        LOOP
                            carrier_value := benefit_obj -> 'values' ->> carrier_item;
                            IF carrier_value IS NOT NULL AND trim(carrier_value) != '' AND carrier_value != '-' THEN
                                carrier_has_data := TRUE;
                                EXIT;
                            END IF;
                        END LOOP;

                        -- Only add benefit if it has actual data
                        IF carrier_has_data THEN
                            benefits_array := benefits_array || jsonb_build_array(benefit_obj);
                        END IF;
                    END;
                END LOOP;

                -- Only create section object if it has benefits (skip empty sections)
                IF jsonb_array_length(benefits_array) > 0 THEN
                    combined_sections_map := combined_sections_map || jsonb_build_object(
                        section_key_item || '_' || combined_class_name,
                        jsonb_build_object(
                            'name', section_display_name,
                            'id', section_id,
                            'benefits', benefits_array,
                            'sort_key', section_key_item,
                            'class_name', combined_class_name
                        )
                    );
                END IF;
            END LOOP;
        END LOOP;
    END;

    -- Step 5: Order sections like original plan design
    DECLARE
        temp_sections_array JSONB := '[]'::jsonb;
        ordered_section RECORD;
    BEGIN
        -- Order sections by config order numbers, then by class name
        FOR ordered_section IN
            WITH section_data AS (
                SELECT
                    value as section_data,
                    value->>'sort_key' as section_key,
                    value->>'class_name' as class_name
                FROM jsonb_each(combined_sections_map)
            )
            SELECT
                section_data,
                COALESCE(
                    (config_plan_details -> section_key ->> 'order')::integer,
                    999999
                ) as sort_order,
                section_key,
                class_name
            FROM section_data
            ORDER BY
                sort_order ASC,
                section_key ASC,
                -- Order by class name alphabetically: ALL first, then by first letter (A,C,D before B)
                CASE
                    WHEN class_name = 'ALL' THEN 0
                    ELSE ascii(substring(class_name, 1, 1)) - 64  -- Sort by first letter: A=1, B=2, etc.
                END ASC,
                class_name ASC
        LOOP
            -- Remove metadata fields and add to final sections array
            section_obj := ordered_section.section_data - 'sort_key' - 'class_name';
            all_sections := all_sections || jsonb_build_array(section_obj);
        END LOOP;
    END;

    -- Step 6: Calculate total benefits and sections for pagination decision
    total_benefits := 0;
    total_sections := jsonb_array_length(all_sections);
    FOR section_idx IN 0..jsonb_array_length(all_sections)-1 LOOP
        total_benefits := total_benefits + jsonb_array_length(all_sections -> section_idx -> 'benefits');
    END LOOP;

    -- If all sections fit in one page (considering both benefits and sections), return single page
    -- Each section adds visual weight, so we count sections + benefits for better pagination
    IF (total_benefits + total_sections) <= MAX_BENEFITS_PER_PAGE THEN
        RETURN jsonb_build_array(
            jsonb_build_object(
                'carriers', ordered_carriers_array,
                'sections', all_sections
            )
        );
    END IF;

    -- Pagination logic - never break sections, only add complete sections to pages
    current_page_benefits := 0;
    current_page_sections_count := 0;
    current_page_sections := '[]'::jsonb;
    result_pages := '[]'::jsonb;

    FOR section_idx IN 0..jsonb_array_length(all_sections)-1 LOOP
        section_obj := all_sections -> section_idx;
        benefits_array := section_obj -> 'benefits';

        -- Get the number of benefits in this section
        DECLARE
            section_benefit_count INTEGER := jsonb_array_length(benefits_array);
        BEGIN
            -- Check if adding this complete section would exceed the page limit
            -- Count both benefits and sections (each section adds visual weight)
            IF current_page_benefits > 0 AND (current_page_benefits + current_page_sections_count + section_benefit_count + 1) > MAX_BENEFITS_PER_PAGE THEN
                -- Current page is full, create a new page
                page_object := jsonb_build_object(
                    'carriers', ordered_carriers_array,
                    'sections', current_page_sections
                );
                result_pages := result_pages || jsonb_build_array(page_object);

                -- Reset for new page
                current_page_sections := '[]'::jsonb;
                current_page_benefits := 0;
                current_page_sections_count := 0;
            END IF;

            -- Add the complete section to current page
            -- Fix: Add section object directly to array, not wrapped in another array
            current_page_sections := current_page_sections || section_obj;
            current_page_benefits := current_page_benefits + section_benefit_count;
            current_page_sections_count := current_page_sections_count + 1;
        END;
    END LOOP;

    -- Add the final page if it has any sections
    IF jsonb_array_length(current_page_sections) > 0 THEN
        page_object := jsonb_build_object(
            'carriers', ordered_carriers_array,
            'sections', current_page_sections
        );
        result_pages := result_pages || jsonb_build_array(page_object);
    END IF;

    RETURN result_pages;

END;
$$;
]]>
        </sql>